{"buildFiles": ["/Users/<USER>/FlutterDev/flutter/packages/flutter_tools/gradle/src/main/groovy/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/projects/lefkas_local_guide/android/app/.cxx/Debug/3o3c3068/arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Users/<USER>/Downloads/projects/lefkas_local_guide/android/app/.cxx/Debug/3o3c3068/arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}