{"project_info": {"project_number": "96743804857", "project_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "storage_bucket": "lefkastopguide.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:96743804857:android:bbf0c58be7e8c806647068", "android_client_info": {"package_name": "com.rixxsol.lefkasadmin"}}, "oauth_client": [{"client_id": "96743804857-e8hhnen0n8omnpj3334vui12kenc9f0l.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyADQxFaAiuz0KI1pvDSRrFszwgbpKnFZug"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "96743804857-e8hhnen0n8omnpj3334vui12kenc9f0l.apps.googleusercontent.com", "client_type": 3}, {"client_id": "96743804857-ths5haacsea7cabp6jp8kplbdvtas3rq.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.rixxsol.localguide"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:96743804857:android:4cb82c12c83bafb0647068", "android_client_info": {"package_name": "com.rixxsol.localguide"}}, "oauth_client": [{"client_id": "96743804857-822lvuhnbqcldbunuodqd970urg1mq2p.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.rixxsol.localguide", "certificate_hash": "a0202ff3e1b98d8d9eaaf2dd38de87058e171a76"}}, {"client_id": "96743804857-8dpdel3dsfaatto3ja6ifvgnc2k9gb9h.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.rixxsol.localguide", "certificate_hash": "1b384fe137a575fc98aec2907328505f68a94741"}}, {"client_id": "96743804857-e8hhnen0n8omnpj3334vui12kenc9f0l.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyADQxFaAiuz0KI1pvDSRrFszwgbpKnFZug"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "96743804857-e8hhnen0n8omnpj3334vui12kenc9f0l.apps.googleusercontent.com", "client_type": 3}, {"client_id": "96743804857-ths5haacsea7cabp6jp8kplbdvtas3rq.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.rixxsol.localguide"}}]}}}], "configuration_version": "1"}